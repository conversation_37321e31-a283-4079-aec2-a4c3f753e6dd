{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/apps/server-mcp", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "downlevelIteration": true, "module": "ES2022", "target": "ES2022", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["node"]}, "files": ["global.d.ts"], "include": ["src/electron-main.ts", "src/preload/*.ts"], "exclude": ["jest.config.ts", "src/**/*.test.ts", "src/**/*.spec.ts"]}