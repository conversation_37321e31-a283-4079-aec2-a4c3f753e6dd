{"name": "@gauzy/server-mcp", "version": "0.1.0", "description": "Ever Gauzy MCP Server - Electron Desktop App with MCP Server integration", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "license": "AGPL-3.0", "private": true, "scripts": {"build:electron": "yarn nx build server-mcp", "build:electron:prod": "yarn nx build server-mcp --configuration=production", "build:electron:dev": "yarn nx build server-mcp --configuration=development", "start": "yarn nx serve server-mcp"}, "devDependencies": {"@electron/rebuild": "^3.2.10", "@types/node": "^22.14.0", "electron": "^30.0.1", "@electron/notarize": "^2.5.0", "electron-builder": "^24.13.3", "electron-installer-dmg": "^4.0.0", "electron-packager": "^17.1.1", "electron-reload": "~1.5.0", "node-polyfill-webpack-plugin": "^1.1.4", "terser-webpack-plugin": "^5.3.14", "rimraf": "^3.0.2", "typescript": "^5.8.3"}, "dependencies": {"@gauzy/mcp-server": "^0.1.0", "@modelcontextprotocol/sdk": "^1.13.1", "axios": "^1.7.0", "dotenv": "^16.4.5", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "electron-util": "^0.18.1", "zod": "^3.25.67", "custom-electron-titlebar": "^4.2.8"}}