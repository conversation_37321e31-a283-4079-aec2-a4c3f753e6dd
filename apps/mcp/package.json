{"name": "@gauzy/mcp", "version": "0.1.0", "description": "Ever Gauzy MCP Server - Standalone Model Context Protocol Server", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "license": "AGPL-3.0", "private": true, "type": "module", "scripts": {"build": "npx rimraf build && npx tsc --project tsconfig.json", "build:prod": "yarn nx build mcp --configuration=production", "build:dev": "yarn nx build mcp --configuration=development", "start": "yarn nx serve mcp", "test": "node build/index.js --test"}, "dependencies": {"@gauzy/mcp-server": "^0.1.0", "@modelcontextprotocol/sdk": "^1.13.1", "electron-log": "^4.4.8"}, "devDependencies": {"@types/node": "^22.14.0", "rimraf": "^3.0.2", "typescript": "^5.8.3"}}